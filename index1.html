<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Star Advisors Top9</title>
	<style type="text/css">
		html,body{
			padding: 0;
			margin: 0;
		}
		.container {
			background-image: url(images/bg.png);
			background-size: 100% 100%;
			background-repeat: no-repeat;
			max-width: 600px;
			min-width: 500px;
			height: 1660px;
			margin: 0 auto;
			background-color: #12021c;
			position: relative;
		}
		.main {
			position: absolute;
		    top: 39%;
		    width: 100%;
		}
		.top3{
			display: flex;
			height:330px;
			gap: 3%;
			width: 500px;
			margin: auto;
		}
		.rank-date {
		    position: absolute;
		    top: 30%;
		    left: 50%;
		    transform: translate(-50%, -50%);
		    color: #FFF;
			text-shadow: 1px 1px 0px #372813, 0px 0px 30px #FFD724;
			font-family: "Source Sans Pro";
			font-size: 16px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;
			letter-spacing: 1px;
		}
		
		.helped {
			font-family: Source Sans Pro;
			font-weight: 400;
			font-size: 13px;
			line-height: 100%;
			letter-spacing: 0%;
			text-align: center;
			vertical-align: middle;
			color: #A38E8D;
			margin: 20px 0 0 0;
		}
		.go-btn1 {
			width: 150px;
		    height: 42px;
		    margin: 7px 0 0 15px;
		    background-image: url(images/btn-1.png);
		    background-repeat: no-repeat;
		    background-size: 100% 100%;
		    border: none;
		    border-radius: 22px;
		    float: left;
		}
		.go-btn2 {
			width: 120px;
		    height: 32px;
		    background-image: url(images/btn-2.png);
		    background-repeat: no-repeat;
		    background-size: 100% 100%;
		    border: none;
		    border-radius: 22px;
		    float: left;
		    margin: 15px 0 0 8px;
		}
		.avatar1{
			width: 104px;
		    height: 104px;
		    top: 70px;
		    left: 28px;
		    border-radius: 90px;
		    border-width: 1px;
		    position: absolute;
		    object-fit: cover;
		}
		.avatar2{
			width: 148px;
		    height: 148px;
		    top: 18px;
		    left: 185px;
		    border-radius: 90px;
		    border-width: 1px;
		    position: absolute;
		    object-fit: cover;
		}
		.avatar3{
			width: 104px;
		    height: 104px;
		    top: 70px;
		    left: 385px;
		    border-radius: 90px;
		    border-width: 1px;
		    position: absolute;
		    object-fit: cover;
		}
		.name1{
			position: absolute;
		    top: 215px;
		    left: 20px;
		    max-width: 116px;
		    color: #0F1142;
			font-weight: 700;
			font-size: 13.68px;
			line-height: 100%;
			text-align: center;
			vertical-align: middle;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.name2{
			position: absolute;
		    top: 210px;
		    left: 190px;
		    max-width: 116px;
		    color: #0F1142;
			font-weight: 700;
			font-size: 15.68px;
			line-height: 100%;
			text-align: center;
			vertical-align: middle;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.name3{
			position: absolute;
		    top: 215px;
		    left: 385px;
		    max-width: 116px;
		    max-width: 116px;
		    color: #0F1142;
			font-weight: 700;
			font-size: 13.68px;
			line-height: 100%;
			text-align: center;
			vertical-align: middle;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.rank {
			list-style: none;
			padding: 20px 0 20px 0;
			margin: 0 10px;
		}
		.rank-item {
		    display: flex;
		    height: 90px;
		    padding: 6px 0;
		}
		.rank-idx {
			flex:12%;
			text-align: center;
			font-family: My Soul;
			font-weight: 400;
			font-size: 30px;
			line-height: 100%;
			letter-spacing: 0%;
			text-align: center;
			vertical-align: middle;
			color: #F8E4C5;
			height: 90px;
    		line-height: 90px;
		}
		.rank-avatar-c{
			background-image: url(images/top4-bg.png);
    		background-repeat: no-repeat;
    		background-size: 100% 100%;
    		width:90px;
    		height:90px
		}
		.rank-avatar{
			width: 80px;
		    height: 80px;
		    border-radius: 90px;
		    border-width: 1px;
		    object-fit: cover;
		    margin: 5px 0 0 8px;
		}
		.rank-name {
			color: #FFFFFF;
		    font-family: Philosopher;
			font-weight: 700;
			font-size: 18px;
			line-height: 100%;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			margin-top: 12px;
		}
		.rank-btn{
			width: 120px;
		    height: 32px;
		    background-image: url(images/btn-2.png);
		    background-repeat: no-repeat;
		    background-size: 100% 100%;
		    border: none;
		    border-radius: 22px;
		    margin: 30px 0 0 8px;
		}
	</style>
</head>
<body>
	<div class="container">
		<div>
			<span class="rank-date">Fberuary 09, 2025</span>
		</div>
		<div class="main">
			<div class="top3">
				<div style="flex: 3;margin-top: 54px;" id="top2">
					<div style="height: 186px;background-image: url(images/top2-bg.png);background-size: 100% 100%;">
						<img src="http://cdn.allstarmet.com/thumbnail/thumbnail/thumbnail/starmet/pro/50011234620241223/458f6fce62e6fee768880513f5539167.jpg_540x0.jpg_540x0.jpg_540x0.jpg" class="avatar1">
						<div class="name1">Psychic Grace</div>
					</div>
					<div class="helped">Helped 120+ Peoples</div>
					<a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>
				</div>
				<div style="flex: 4" id="top1">
					<div style="height: 240px;background-image: url(images/top1-bg.png);background-size: 100% 100%;">
						<img src="http://cdn.allstarmet.com/thumbnail/starmet/pro/50011121820250627/9a7097bc48e47747e253c1967024fd76.jpg_540x0.jpg" class="avatar2">
						<div class="name2">Spirit Whitney</div>
					</div>
					<div class="helped">Helped 120+ Peoples</div>
					<a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn1"></a>
				</div>
				<div style="flex: 3;margin-top: 54px;" id="top3">
					<div style="height: 186px;background-image: url(images/top3-bg.png);background-size: 100% 100%;">
						<img src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500112381/media/20250113/7a7f3fd9e9e715131c847651f5f9d3f7.jpg_540x0.jpg" class="avatar3">
						<div class="name3">Danielle 💜</div>
					</div>
					<div class="helped">Helped 120+ Peoples</div>
					<a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>
				</div>
			</div>
			<ul class="rank" id="rank-list">
				<li class="rank-item">
					<div class="rank-idx">4</div>
					<div style="flex: 22%">
						<div class="rank-avatar-c">
							<img class="rank-avatar" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg" />
						</div>
					</div>
					<div class="rank-name" style="flex:36%;margin-left: 12px;">
						<p class="rank-name">Spiritual Lily</p>
						<p class="helped" style="text-align: left!important;">Helped 26+ Peoples</p>
					</div>
					<div class="rank-btn" style="flex:30%;display: contents;">
						<a href="#" class="rank-btn"></a>
					</div>
				</li>
				<li class="rank-item">
					<div class="rank-idx">5</div>
					<div style="flex: 22%">
						<div class="rank-avatar-c">
							<img class="rank-avatar" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg" />
						</div>
					</div>
					<div class="rank-name" style="flex:36%;margin-left: 12px;">
						<p class="rank-name">Spiritual Lily</p>
						<p class="helped" style="text-align: left!important;">Helped 26+ Peoples</p>
					</div>
					<div class="rank-btn" style="flex:30%;display: contents;">
						<a href="#" class="rank-btn"></a>
					</div>
				</li>
				<li class="rank-item">
					<div class="rank-idx">6</div>
					<div style="flex: 22%">
						<div class="rank-avatar-c">
							<img class="rank-avatar" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg" />
						</div>
					</div>
					<div class="rank-name" style="flex:36%;margin-left: 12px;">
						<p class="rank-name">Spiritual Lily</p>
						<p class="helped" style="text-align: left!important;">Helped 26+ Peoples</p>
					</div>
					<div class="rank-btn" style="flex:30%;display: contents;">
						<a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="rank-btn"></a>
					</div>
				</li>
				<li class="rank-item">
					<div class="rank-idx">7</div>
					<div style="flex: 22%">
						<div class="rank-avatar-c">
							<img class="rank-avatar" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg" />
						</div>
					</div>
					<div class="rank-name" style="flex:36%;margin-left: 12px;">
						<p class="rank-name">Spiritual Lily</p>
						<p class="helped" style="text-align: left!important;">Helped 26+ Peoples</p>
					</div>
					<div class="rank-btn" style="flex:30%;display: contents;">
						<a href="#" class="rank-btn"></a>
					</div>
				</li>
				<li class="rank-item">
					<div class="rank-idx">8</div>
					<div style="flex: 22%">
						<div class="rank-avatar-c">
							<img class="rank-avatar" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg" />
						</div>
					</div>
					<div class="rank-name" style="flex:36%;margin-left: 12px;">
						<p class="rank-name">Spiritual Lily</p>
						<p class="helped" style="text-align: left!important;">Helped 26+ Peoples</p>
					</div>
					<div class="rank-btn" style="flex:30%;display: contents;">
						<a href="#" class="rank-btn"></a>
					</div>
				</li>
				<li class="rank-item">
					<div class="rank-idx">9</div>
					<div style="flex: 22%">
						<div class="rank-avatar-c">
							<img class="rank-avatar" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg" />
						</div>
					</div>
					<div class="rank-name" style="flex:36%;margin-left: 12px;">
						<p class="rank-name">Spiritual Lily</p>
						<p class="helped" style="text-align: left!important;">Helped 26+ Peoples</p>
					</div>
					<div class="rank-btn" style="flex:30%;display: contents;">
						<a href="#" class="rank-btn"></a>
					</div>
				</li>
			</ul>
		</div>
	</div>
	
</body>
</html>

<script type="text/javascript" src="https://www.healtalkapp.com/activity/rank/js/jquery-3.2.1.min.js"></script>
<script type="text/javascript">
	const host = "https://test.api.allstarmet.com";
	// const host = "https://api.healtalkapp.com";

	initData();

    function initData() {
		$.ajax({
		    type: "get",
		    url: host + '/web/augur/weekly/rank?appId=10', 
		    async: true,
		    dataType: "json",
		    success:function(res){ 
		       	let response = res.data;
				let content = "";
				response.forEach(function(item, index, array){
					/*let rankIndex = index + 1;
				    let rankBgIndex = index < 3 ? index + 1 : 'o';
				    let rankTextIndex = index < 3 ? 'rank-index' : 'rank-index-o';
				    content += '<li class="rank-item" style="background-image: url(https://www.healtalkapp.com/activity/rank/images/he-t'+rankBgIndex+'.png);"><div class="'+rankTextIndex+'">Top'+rankIndex+'</div><div class="rank-detail"><img class="avatar" src="'+item.avatar+'"><div class="augur-info"><div class="username">'+item.username+'</div><div class="helped">Helped '+item.serviceUsers+'+ </div><img class="go-btn" onclick="goStore()" src="https://www.healtalkapp.com/activity/rank/images/he-btn.png"></div></div></li>';*/
				    if (index == 0) {
				    	let top1Content = '<div style="height: 240px;background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top1-bg.png);background-size: 100% 100%;"><img src="'+item.avatar+'" class="avatar2"><div class="name2">'+item.username+'</div></div><div class="helped">Helped '+item.serviceUsers+'+ Peoples</div><a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn1"></a>';
				    	$("#top1").html(top1Content);
				    	console.log("第一个填充");
				    } else if (index == 1) {
				    	let top2Content = '<div style="height: 186px;background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top2-bg.png);background-size: 100% 100%;"><img src="'+item.avatar+'" class="avatar1"><div class="name1">'+item.username+'</div></div><div class="helped">Helped '+item.serviceUsers+'+ Peoples</div><a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>';
				    	$("#top2").html(top2Content);
				    	console.log("第2个填充");
				    } else if (index == 2) {
				    	let top3Content = '<div style="height: 186px;background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top3-bg.png);background-size: 100% 100%;"><img src="'+item.avatar+'" class="avatar3"><div class="name3">'+item.username+'</div></div><div class="helped">Helped '+item.serviceUsers+'+ Peoples</div><a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>';
				    	$("#top3").html(top3Content);
				    	console.log("第3个填充");
				    } else {
				    	let idx = index + 1;
				    	content += '<li class="rank-item"><div class="rank-idx">'+idx+'</div><div style="flex: 22%"><div class="rank-avatar-c"><img class="rank-avatar" src="'+item.avatar+'" /></div></div><div class="rank-name" style="flex:36%;margin-left: 12px;"><p class="rank-name">'+item.username+'</p><p class="helped" style="text-align: left!important;">Helped '+item.serviceUsers+'+ Peoples</p></div><div class="rank-btn" style="flex:30%;display: contents;"><a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="rank-btn"></a></div></li>';
				    }
				 })
				document.getElementById("rank-list").innerHTML = content;
				$("#rank-list").html(content);
				console.log("其它填充");
		    }
		})
 
    }
</script>