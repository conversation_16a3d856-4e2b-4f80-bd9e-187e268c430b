<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Index2 Simple Email Generator (No CORS)</title>
    <script src="https://www.cyanmoonapp.com/activity/rank/js/jquery-3.2.1.min.js"></script>
    <style>
        .generator-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .controls {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
        }
        
        .code-section pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
            max-height: 400px;
        }
    </style>
</head>
<body>
    <div class="generator-container">
        <h1>Index2 Simple Email Generator</h1>
        <p style="color: #666;">无CORS问题的邮件生成器 - 直接使用原始图片URL</p>
        
        <div class="controls">
            <h3>操作控制</h3>
            <button onclick="loadData()">1. 加载数据</button>
            <button onclick="generateSimpleEmail()">2. 生成邮件HTML</button>
            <button onclick="generateImageMapEmail()">3. 生成ImageMap版本</button>
            <button onclick="copyHTML()">4. 复制HTML</button>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>
        
        <div class="result-container" id="result-container">
            <h3>生成的邮件HTML</h3>
            <p style="color: #666;">点击上方按钮开始生成邮件</p>
        </div>
    </div>
    
    <script>
        let rankingData = null;
        let currentHTML = '';
        
        function showStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            setTimeout(() => statusEl.style.display = 'none', 3000);
        }
        
        async function loadData() {
            try {
                // 尝试加载真实数据
                const response = await fetch('https://api.cyanmoonapp.com/web/augur/weekly/rank?appId=10');
                if (response.ok) {
                    const res = await response.json();
                    rankingData = res.data;
                } else {
                    throw new Error('API failed');
                }
            } catch (error) {
                // 使用模拟数据
                rankingData = {
                    date: "February 09, 2025",
                    rank: [
                        { username: "Psychic Bianca", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/50011234620250220/571933f28fd8f6d63a440d5444951f8b.jpg_540x0.jpg", serviceUsers: 144 },
                        { username: "Psychic Lumina", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500112381/media/20250113/7a7f3fd9e9e715131c847651f5f9d3f7.jpg_540x0.jpg", serviceUsers: 200 },
                        { username: "Psychic Mandy", avatar: "http://cdn.allstarmet.com/starmet/pro/50011165620240427/49462087e0819829a7ba90d78be3c63c.jpg", serviceUsers: 74 },
                        { username: "Spiritual Lily", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 65 },
                        { username: "Mystic Rose", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 58 },
                        { username: "Oracle Sam", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 52 }
                    ]
                };
            }
            showStatus('数据加载完成！');
        }
        
        function generateSimpleEmail() {
            if (!rankingData) {
                showStatus('请先加载数据', 'error');
                return;
            }
            
            currentHTML = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Star Advisors Weekly Ranking</title>
    <style type="text/css">
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #12021c;
            font-family: Arial, Helvetica, sans-serif;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #12021c;
        }
        .main-bg {
            width: 500px;
            height: 1700px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/bg.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-color: #12021c;
            position: relative;
            margin: 0 auto;
        }
        .date-section {
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 32px;
            font-weight: 600;
            letter-spacing: 1px;
            text-align: center;
            text-shadow: 1px 1px 0px #372813, 0px 0px 30px #FFD724;
        }
        .top3-section {
            position: absolute;
            top: 39%;
            width: 100%;
        }
        .top3-container {
            width: 500px;
            height: 330px;
            margin: auto;
            position: relative;
        }
        /* Top 2 */
        .top2-card {
            position: absolute;
            left: 0;
            top: 54px;
            width: 156px;
        }
        .top2-bg {
            width: 156px;
            height: 186px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top2-bg.png);
            background-size: 100% 100%;
            position: relative;
        }
        .avatar1 {
            width: 104px;
            height: 104px;
            top: 16px;
            left: 28px;
            border-radius: 50%;
            position: absolute;
            object-fit: cover;
        }
        .name1 {
            position: absolute;
            top: 162px;
            left: 20px;
            max-width: 116px;
            color: #0f1142;
            font-weight: 700;
            font-size: 13.68px;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        /* Top 1 */
        .top1-card {
            position: absolute;
            left: 172px;
            top: 0;
            width: 156px;
        }
        .top1-bg {
            width: 156px;
            height: 240px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top1-bg.png);
            background-size: 100% 100%;
            position: relative;
        }
        .avatar2 {
            width: 148px;
            height: 148px;
            top: 20px;
            left: 4px;
            border-radius: 50%;
            position: absolute;
            object-fit: cover;
        }
        .name2 {
            position: absolute;
            top: 210px;
            left: 4px;
            max-width: 148px;
            color: #0f1142;
            font-weight: 700;
            font-size: 15.68px;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        /* Top 3 */
        .top3-card {
            position: absolute;
            left: 344px;
            top: 54px;
            width: 156px;
        }
        .top3-bg {
            width: 156px;
            height: 186px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top3-bg.png);
            background-size: 100% 100%;
            position: relative;
        }
        .avatar3 {
            width: 104px;
            height: 104px;
            top: 20px;
            left: 26px;
            border-radius: 50%;
            position: absolute;
            object-fit: cover;
        }
        .name3 {
            position: absolute;
            top: 162px;
            left: 26px;
            max-width: 104px;
            color: #0f1142;
            font-weight: 700;
            font-size: 13.68px;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .helped {
            color: #a38e8d;
            font-size: 13px;
            text-align: center;
            margin: 20px 0 0 0;
        }
        .go-btn1 {
            width: 150px;
            height: 42px;
            margin: 7px 0 0 3px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/btn-1.png);
            background-size: 100% 100%;
            border: none;
            border-radius: 22px;
            display: block;
        }
        .go-btn2 {
            width: 120px;
            height: 32px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/btn-2.png);
            background-size: 100% 100%;
            border: none;
            border-radius: 22px;
            margin: 15px 0 0 18px;
            display: block;
        }
        /* Rank list */
        .rank-list {
            position: absolute;
            top: 70%;
            width: 100%;
            padding: 0 10px;
        }
        .rank-item {
            display: table;
            width: 100%;
            height: 90px;
            margin-bottom: 6px;
        }
        .rank-number {
            display: table-cell;
            width: 60px;
            text-align: center;
            vertical-align: middle;
            color: #f8e4c5;
            font-size: 30px;
            font-weight: bold;
        }
        .rank-avatar-cell {
            display: table-cell;
            width: 90px;
            vertical-align: middle;
        }
        .rank-avatar-bg {
            width: 90px;
            height: 90px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top4-bg.png);
            background-size: 100% 100%;
            position: relative;
        }
        .rank-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin: 5px 0 0 5px;
        }
        .rank-info {
            display: table-cell;
            vertical-align: middle;
            padding-left: 12px;
        }
        .rank-name {
            color: #ffffff;
            font-weight: 700;
            font-size: 18px;
            margin: 0 0 5px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .rank-helped {
            color: #a38e8d;
            font-size: 13px;
            margin: 0;
        }
        .rank-btn-cell {
            display: table-cell;
            width: 120px;
            text-align: center;
            vertical-align: middle;
        }
        .rank-btn {
            width: 120px;
            height: 32px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/btn-2.png);
            background-size: 100% 100%;
            border: none;
            border-radius: 22px;
            display: inline-block;
        }
        .unsubscribe {
            position: absolute;
            bottom: 20px;
            left: 42%;
            text-align: center;
        }
        .unsubscribe-a {
            color: #c7c3c3;
            text-decoration: none;
            font-size: 13px;
        }
        @media screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            .main-bg {
                width: 100% !important;
                height: auto !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="main-bg">
            <!-- Date -->
            <div class="date-section">${rankingData.date}</div>
            
            <!-- Top 3 Section -->
            <div class="top3-section">
                <div class="top3-container">
                    <!-- Top 2 -->
                    <div class="top2-card">
                        <div class="top2-bg">
                            <img src="${rankingData.rank[1].avatar}" alt="${rankingData.rank[1].username}" class="avatar1">
                            <div class="name1">${rankingData.rank[1].username}</div>
                        </div>
                        <div class="helped">Helped ${rankingData.rank[1].serviceUsers}+ Peoples</div>
                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>
                    </div>
                    
                    <!-- Top 1 -->
                    <div class="top1-card">
                        <div class="top1-bg">
                            <img src="${rankingData.rank[0].avatar}" alt="${rankingData.rank[0].username}" class="avatar2">
                            <div class="name2">${rankingData.rank[0].username}</div>
                        </div>
                        <div class="helped">Helped ${rankingData.rank[0].serviceUsers}+ Peoples</div>
                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn1"></a>
                    </div>
                    
                    <!-- Top 3 -->
                    <div class="top3-card">
                        <div class="top3-bg">
                            <img src="${rankingData.rank[2].avatar}" alt="${rankingData.rank[2].username}" class="avatar3">
                            <div class="name3">${rankingData.rank[2].username}</div>
                        </div>
                        <div class="helped">Helped ${rankingData.rank[2].serviceUsers}+ Peoples</div>
                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>
                    </div>
                </div>
            </div>
            
            <!-- Rank List -->
            <div class="rank-list">
                ${rankingData.rank.slice(3).map((user, index) => `
                <div class="rank-item">
                    <div class="rank-number">${index + 4}</div>
                    <div class="rank-avatar-cell">
                        <div class="rank-avatar-bg">
                            <img src="${user.avatar}" alt="${user.username}" class="rank-avatar">
                        </div>
                    </div>
                    <div class="rank-info">
                        <div class="rank-name">${user.username}</div>
                        <div class="rank-helped">Helped ${user.serviceUsers}+ Peoples</div>
                    </div>
                    <div class="rank-btn-cell">
                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="rank-btn"></a>
                    </div>
                </div>
                `).join('')}
            </div>
            
            <!-- Unsubscribe -->
            <div class="unsubscribe">
                <a href="unsubscribe_link" class="unsubscribe-a">unsubscribe</a>
            </div>
        </div>
    </div>
</body>
</html>`;
            
            displayResult(currentHTML, '简单邮件版本（无CORS问题）');
            showStatus('邮件HTML生成成功！');
        }

        function generateImageMapEmail() {
            if (!rankingData) {
                showStatus('请先加载数据', 'error');
                return;
            }

            // 定义可点击区域（基于原始布局的像素位置）
            const clickableAreas = [
                // Top 2 按钮
                { coords: "18,260,138,292", href: "https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en", alt: "View Psychic Lumina" },
                // Top 1 按钮
                { coords: "175,267,325,309", href: "https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en", alt: "View Psychic Bianca" },
                // Top 3 按钮
                { coords: "362,260,482,292", href: "https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en", alt: "View Psychic Mandy" },
                // 排名列表按钮（4-9名）
                { coords: "380,1200,500,1232", href: "https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en", alt: "View Rank 4" },
                { coords: "380,1300,500,1332", href: "https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en", alt: "View Rank 5" },
                { coords: "380,1400,500,1432", href: "https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en", alt: "View Rank 6" }
            ];

            const mapAreas = clickableAreas.map(area =>
                `<area shape="rect" coords="${area.coords}" href="${area.href}" alt="${area.alt}" title="${area.alt}">`
            ).join('\n            ');

            currentHTML = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Star Advisors Weekly Ranking</title>
    <style type="text/css">
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #12021c;
            font-family: Arial, Helvetica, sans-serif;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #12021c;
        }
        @media screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            .ranking-image {
                width: 100% !important;
                height: auto !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td align="center" style="padding: 20px;">
                    <!-- 这里需要替换为实际的Canvas生成的图片 -->
                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                         usemap="#ranking-map"
                         alt="Star Advisors Weekly Ranking"
                         class="ranking-image"
                         style="max-width: 500px; width: 100%; height: auto; display: block; background: #12021c;">
                    <map name="ranking-map">
                        ${mapAreas}
                    </map>
                    <p style="color: #fff; text-align: center; margin-top: 20px;">
                        Weekly Ranking - ${rankingData.date}<br>
                        <span style="font-size: 14px;">
                            1st: ${rankingData.rank[0].username} |
                            2nd: ${rankingData.rank[1].username} |
                            3rd: ${rankingData.rank[2].username}
                        </span>
                    </p>
                </td>
            </tr>
            <tr>
                <td align="center" style="padding: 10px; color: #c7c3c3; font-size: 12px;">
                    <p style="margin: 0;">
                        <a href="https://app.com/ranking" style="color: #c7c3c3;">View Interactive Version</a> |
                        <a href="unsubscribe_link" style="color: #c7c3c3;">Unsubscribe</a>
                    </p>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;

            displayResult(currentHTML, 'ImageMap邮件版本（需要替换图片）');
            showStatus('ImageMap邮件生成成功！注意：需要手动替换图片URL');
        }

        function displayResult(html, title) {
            const container = document.getElementById('result-container');
            container.innerHTML = `
                <h3>${title}</h3>
                <div style="margin: 20px 0;">
                    <iframe style="width: 100%; height: 600px; border: 1px solid #ddd; border-radius: 4px;"
                            srcdoc="${html.replace(/"/g, '&quot;')}"></iframe>
                </div>
                <details>
                    <summary><strong>查看HTML代码</strong></summary>
                    <pre>${html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
                </details>
            `;
        }

        function copyHTML() {
            if (!currentHTML) {
                showStatus('没有可复制的内容', 'error');
                return;
            }

            navigator.clipboard.writeText(currentHTML).then(() => {
                showStatus('HTML代码已复制到剪贴板！');
            }).catch(err => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = currentHTML;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus('HTML代码已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
