<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Canvas Email Generator</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        .generator-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .preview-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        .result-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            background: white;
        }
        
        .clickable-overlay {
            position: relative;
            display: inline-block;
        }
        
        .click-area {
            position: absolute;
            background: rgba(255, 0, 0, 0.1);
            border: 1px dashed red;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .click-area:hover {
            background: rgba(255, 0, 0, 0.3);
        }
        
        .email-safe-version {
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 模拟原始页面样式 */
        .demo-container {
            width: 500px;
            height: 400px;
            background: linear-gradient(135deg, #12021c, #2a1a3a);
            position: relative;
            margin: 0 auto;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .demo-header {
            text-align: center;
            padding: 20px;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .demo-top3 {
            display: flex;
            justify-content: space-around;
            padding: 20px;
        }
        
        .demo-rank-item {
            text-align: center;
            color: white;
        }
        
        .demo-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #FFD724;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #000;
        }
        
        .demo-btn {
            background: #FFD724;
            color: #000;
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            margin-top: 10px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 12px;
        }
        
        .demo-rank-list {
            padding: 20px;
        }
        
        .demo-rank-row {
            display: flex;
            align-items: center;
            color: white;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .demo-rank-number {
            width: 30px;
            font-size: 20px;
            font-weight: bold;
            color: #FFD724;
        }
        
        .demo-rank-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #FFD724;
            margin: 0 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #000;
            font-size: 12px;
        }
        
        .demo-rank-info {
            flex: 1;
        }
        
        .demo-rank-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .demo-rank-helped {
            font-size: 12px;
            color: #A38E8D;
        }
    </style>
</head>
<body>
    <div class="generator-container">
        <h1>Canvas Email Generator - 可点击图片邮件</h1>
        
        <div class="controls">
            <button onclick="generateCanvasEmail()">生成Canvas邮件</button>
            <button onclick="generateImageMapEmail()">生成ImageMap邮件</button>
            <button onclick="generateLayeredEmail()">生成分层邮件</button>
            <button onclick="showEmailSafeVersion()">显示邮件安全版本</button>
        </div>
        
        <!-- 原始内容 -->
        <div class="preview-section">
            <h3>原始动态内容</h3>
            <div id="original-content" class="demo-container">
                <div class="demo-header">
                    <div id="dynamic-date">Loading...</div>
                </div>
                
                <div class="demo-top3">
                    <div class="demo-rank-item">
                        <div class="demo-avatar">2</div>
                        <div id="rank2-name">Loading...</div>
                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="demo-btn">View</a>
                    </div>
                    
                    <div class="demo-rank-item">
                        <div class="demo-avatar">1</div>
                        <div id="rank1-name">Loading...</div>
                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="demo-btn">View</a>
                    </div>
                    
                    <div class="demo-rank-item">
                        <div class="demo-avatar">3</div>
                        <div id="rank3-name">Loading...</div>
                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="demo-btn">View</a>
                    </div>
                </div>
                
                <div class="demo-rank-list" id="rank-list">
                    <!-- 动态内容将在这里生成 -->
                </div>
            </div>
        </div>
        
        <!-- 结果显示区域 -->
        <div class="result-container" id="result-container">
            <h3>生成的邮件内容将显示在这里</h3>
        </div>
    </div>
    
    <script>
        // 模拟动态数据加载
        const mockData = {
            date: "February 09, 2025",
            rankings: [
                { name: "Spirit Whitney", helped: 120 },
                { name: "Psychic Grace", helped: 95 },
                { name: "Danielle 💜", helped: 88 },
                { name: "Spiritual Lily", helped: 76 },
                { name: "Mystic Rose", helped: 65 },
                { name: "Oracle Sam", helped: 54 }
            ]
        };
        
        // 模拟异步数据加载
        function loadDynamicContent() {
            return new Promise(resolve => {
                setTimeout(() => {
                    // 更新日期
                    document.getElementById('dynamic-date').textContent = mockData.date;
                    
                    // 更新Top 3
                    document.getElementById('rank1-name').textContent = mockData.rankings[0].name;
                    document.getElementById('rank2-name').textContent = mockData.rankings[1].name;
                    document.getElementById('rank3-name').textContent = mockData.rankings[2].name;
                    
                    // 更新排名列表
                    const rankList = document.getElementById('rank-list');
                    rankList.innerHTML = '';
                    
                    for (let i = 3; i < mockData.rankings.length; i++) {
                        const rank = mockData.rankings[i];
                        const rankRow = document.createElement('div');
                        rankRow.className = 'demo-rank-row';
                        rankRow.innerHTML = `
                            <div class="demo-rank-number">${i + 1}</div>
                            <div class="demo-rank-avatar">${rank.name.charAt(0)}</div>
                            <div class="demo-rank-info">
                                <div class="demo-rank-name">${rank.name}</div>
                                <div class="demo-rank-helped">Helped ${rank.helped}+ Peoples</div>
                            </div>
                            <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="demo-btn">View</a>
                        `;
                        rankList.appendChild(rankRow);
                    }
                    
                    resolve();
                }, 1000); // 模拟1秒加载时间
            });
        }
        
        // 获取可点击区域
        function getClickableAreas(container) {
            const areas = [];
            const containerRect = container.getBoundingClientRect();
            
            container.querySelectorAll('a, button').forEach((element, index) => {
                const rect = element.getBoundingClientRect();
                areas.push({
                    id: `area-${index}`,
                    coords: {
                        x: rect.left - containerRect.left,
                        y: rect.top - containerRect.top,
                        width: rect.width,
                        height: rect.height
                    },
                    href: element.href || '#',
                    text: element.textContent.trim()
                });
            });
            
            return areas;
        }
        
        // 生成Canvas邮件
        async function generateCanvasEmail() {
            await loadDynamicContent();
            
            const container = document.getElementById('original-content');
            const canvas = await html2canvas(container, {
                useCORS: true,
                scale: 1
            });
            
            const imageDataUrl = canvas.toDataURL('image/png');
            const areas = getClickableAreas(container);
            
            // 生成Image Map HTML
            const mapAreas = areas.map(area => 
                `<area shape="rect" coords="${area.coords.x},${area.coords.y},${area.coords.x + area.coords.width},${area.coords.y + area.coords.height}" href="${area.href}" alt="${area.text}">`
            ).join('\n            ');
            
            const emailHTML = `
            <div class="email-safe-version">
                <img src="${imageDataUrl}" 
                     usemap="#ranking-map" 
                     alt="Weekly Ranking"
                     style="max-width: 100%; height: auto; display: block;">
                <map name="ranking-map">
                    ${mapAreas}
                </map>
                <p style="text-align: center; margin-top: 10px; font-size: 12px; color: #666;">
                    <a href="https://app.com/ranking">View Interactive Version</a>
                </p>
            </div>
            `;
            
            document.getElementById('result-container').innerHTML = `
                <h3>Canvas + Image Map 邮件版本</h3>
                ${emailHTML}
                <details style="margin-top: 20px;">
                    <summary>查看HTML代码</summary>
                    <pre style="background: #f5f5f5; padding: 10px; overflow-x: auto;"><code>${emailHTML.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
                </details>
            `;
        }
        
        // 生成分层邮件
        async function generateLayeredEmail() {
            await loadDynamicContent();
            
            const container = document.getElementById('original-content');
            const canvas = await html2canvas(container, {
                useCORS: true,
                scale: 1
            });
            
            const imageDataUrl = canvas.toDataURL('image/png');
            const areas = getClickableAreas(container);
            
            // 生成可点击覆盖层
            const clickableOverlays = areas.map((area, index) => `
                <a href="${area.href}" 
                   class="click-area"
                   style="
                       left: ${area.coords.x}px;
                       top: ${area.coords.y}px;
                       width: ${area.coords.width}px;
                       height: ${area.coords.height}px;
                   "
                   title="${area.text}"></a>
            `).join('');
            
            const emailHTML = `
            <div class="email-safe-version">
                <div class="clickable-overlay">
                    <img src="${imageDataUrl}" 
                         alt="Weekly Ranking"
                         style="display: block; max-width: 100%; height: auto;">
                    ${clickableOverlays}
                </div>
                <p style="text-align: center; margin-top: 10px; font-size: 12px; color: #666;">
                    <a href="https://app.com/ranking">View Interactive Version</a>
                </p>
            </div>
            `;
            
            document.getElementById('result-container').innerHTML = `
                <h3>分层覆盖邮件版本</h3>
                <p style="color: #666; font-size: 14px;">鼠标悬停可以看到可点击区域</p>
                ${emailHTML}
                <details style="margin-top: 20px;">
                    <summary>查看HTML代码</summary>
                    <pre style="background: #f5f5f5; padding: 10px; overflow-x: auto;"><code>${emailHTML.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
                </details>
            `;
        }
        
        // 显示邮件安全版本
        function showEmailSafeVersion() {
            const emailHTML = `
            <div class="email-safe-version">
                <p style="text-align: center; color: #666; margin-bottom: 20px;">
                    这是传统的邮件安全版本，使用表格布局和内联样式
                </p>
                <table width="500" cellpadding="0" cellspacing="0" style="margin: 0 auto; background: linear-gradient(135deg, #12021c, #2a1a3a); border-radius: 10px;">
                    <tr>
                        <td style="padding: 20px; text-align: center; color: white; font-size: 18px; font-weight: bold;">
                            February 09, 2025
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 20px;">
                            <table width="100%" cellpadding="10" cellspacing="0">
                                <tr>
                                    <td width="33%" style="text-align: center; color: white;">
                                        <div style="width: 60px; height: 60px; border-radius: 50%; background: #FFD724; margin: 0 auto 10px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #000;">2</div>
                                        <div>Psychic Grace</div>
                                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" style="background: #FFD724; color: #000; padding: 8px 16px; border-radius: 20px; text-decoration: none; display: inline-block; font-size: 12px; margin-top: 10px;">View</a>
                                    </td>
                                    <td width="34%" style="text-align: center; color: white;">
                                        <div style="width: 60px; height: 60px; border-radius: 50%; background: #FFD724; margin: 0 auto 10px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #000;">1</div>
                                        <div>Spirit Whitney</div>
                                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" style="background: #FFD724; color: #000; padding: 8px 16px; border-radius: 20px; text-decoration: none; display: inline-block; font-size: 12px; margin-top: 10px;">View</a>
                                    </td>
                                    <td width="33%" style="text-align: center; color: white;">
                                        <div style="width: 60px; height: 60px; border-radius: 50%; background: #FFD724; margin: 0 auto 10px; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #000;">3</div>
                                        <div>Danielle 💜</div>
                                        <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" style="background: #FFD724; color: #000; padding: 8px 16px; border-radius: 20px; text-decoration: none; display: inline-block; font-size: 12px; margin-top: 10px;">View</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <p style="text-align: center; margin-top: 20px;">
                    <a href="https://app.com/ranking" style="color: #007cba;">View Full Ranking</a>
                </p>
            </div>
            `;
            
            document.getElementById('result-container').innerHTML = `
                <h3>传统邮件安全版本</h3>
                ${emailHTML}
            `;
        }
        
        // 页面加载时初始化
        window.onload = function() {
            loadDynamicContent();
        };
    </script>
</body>
</html>
