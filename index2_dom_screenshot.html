<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Index2 DOM Screenshot Email Generator</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script src="https://www.cyanmoonapp.com/activity/rank/js/jquery-3.2.1.min.js"></script>
    <style>
        .generator-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .controls {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .preview-section {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .original-preview {
            flex: 1;
            border: 1px solid #ddd;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        
        .result-preview {
            flex: 1;
            border: 1px solid #ddd;
            padding: 20px;
            background: white;
            border-radius: 8px;
        }
        
        .code-section pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
            max-height: 400px;
        }
        
        /* 原始页面样式 - 简化版 */
        .container {
            width: 500px;
            height: 1700px;
            background: linear-gradient(135deg, #12021c, #2a1a3a);
            position: relative;
            margin: 0 auto;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .rank-date {
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 32px;
            font-weight: 600;
            letter-spacing: 1px;
            text-align: center;
            text-shadow: 1px 1px 0px #372813, 0px 0px 30px #FFD724;
        }
        
        .main {
            position: absolute;
            top: 39%;
            width: 100%;
        }
        
        .top3 {
            display: flex;
            height: 330px;
            gap: 3%;
            width: 500px;
            margin: auto;
        }
        
        .top-card {
            text-align: center;
            color: white;
            padding: 20px;
            background: rgba(255, 215, 36, 0.1);
            border-radius: 15px;
            border: 2px solid #FFD724;
        }
        
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 10px;
            background: #FFD724;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #000;
        }
        
        .avatar.first {
            width: 100px;
            height: 100px;
        }
        
        .name {
            font-weight: bold;
            margin: 10px 0 5px 0;
        }
        
        .helped {
            color: #A38E8D;
            font-size: 12px;
            margin: 5px 0 15px 0;
        }
        
        .go-btn {
            background: #FFD724;
            color: #000;
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 12px;
        }
        
        .rank-list {
            padding: 20px;
            list-style: none;
            margin: 0;
        }
        
        .rank-item {
            display: flex;
            align-items: center;
            color: white;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .rank-number {
            width: 40px;
            font-size: 24px;
            font-weight: bold;
            color: #FFD724;
        }
        
        .rank-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #FFD724;
            margin: 0 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #000;
            font-size: 14px;
        }
        
        .rank-info {
            flex: 1;
        }
        
        .rank-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .rank-helped {
            font-size: 12px;
            color: #A38E8D;
        }
        
        .unsubscribe {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
        }
        
        .unsubscribe-a {
            color: #c7c3c3;
            text-decoration: none;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="generator-container">
        <h1>Index2 DOM Screenshot Email Generator</h1>
        <p style="color: #666;">使用DOM截图技术，避免Canvas CORS问题</p>
        
        <div class="controls">
            <h3>操作控制</h3>
            <button onclick="loadData()">1. 加载数据</button>
            <button onclick="generateDOMScreenshot()">2. 生成DOM截图邮件</button>
            <button onclick="generateStaticEmail()">3. 生成静态HTML邮件</button>
            <button onclick="copyHTML()">4. 复制HTML</button>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>
        
        <div class="preview-section">
            <div class="original-preview">
                <h3>原始内容预览</h3>
                <div class="container" id="original-container">
                    <div class="rank-date" id="rank-date">February 09, 2025</div>
                    
                    <div class="main">
                        <div class="top3" id="top3-container">
                            <!-- 动态内容将在这里生成 -->
                        </div>
                        
                        <ul class="rank-list" id="rank-list">
                            <!-- 动态内容将在这里生成 -->
                        </ul>
                    </div>
                    
                    <div class="unsubscribe">
                        <a href="unsubscribe_link" class="unsubscribe-a">unsubscribe</a>
                    </div>
                </div>
            </div>
            
            <div class="result-preview">
                <h3>生成的邮件预览</h3>
                <div id="email-result">
                    <p style="color: #666; text-align: center; padding: 40px;">
                        请先点击"加载数据"，然后选择生成方式
                    </p>
                </div>
            </div>
        </div>
        
        <div class="code-section" id="code-section" style="display: none;">
            <details>
                <summary><strong>查看生成的邮件HTML代码</strong></summary>
                <pre id="html-code"></pre>
            </details>
        </div>
    </div>
    
    <script>
        let rankingData = null;
        let currentHTML = '';
        
        function showStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            setTimeout(() => {
                if (type !== 'loading') statusEl.style.display = 'none';
            }, 3000);
        }
        
        async function loadData() {
            showStatus('正在加载数据...', 'loading');
            
            try {
                const response = await fetch('https://api.cyanmoonapp.com/web/augur/weekly/rank?appId=10');
                if (response.ok) {
                    const res = await response.json();
                    rankingData = res.data;
                } else {
                    throw new Error('API failed');
                }
            } catch (error) {
                // 使用模拟数据
                rankingData = {
                    date: "February 09, 2025",
                    rank: [
                        { username: "Psychic Bianca", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/50011234620250220/571933f28fd8f6d63a440d5444951f8b.jpg_540x0.jpg", serviceUsers: 144 },
                        { username: "Psychic Lumina", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500112381/media/20250113/7a7f3fd9e9e715131c847651f5f9d3f7.jpg_540x0.jpg", serviceUsers: 200 },
                        { username: "Psychic Mandy", avatar: "http://cdn.allstarmet.com/starmet/pro/50011165620240427/49462087e0819829a7ba90d78be3c63c.jpg", serviceUsers: 74 },
                        { username: "Spiritual Lily", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 65 },
                        { username: "Mystic Rose", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 58 },
                        { username: "Oracle Sam", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 52 }
                    ]
                };
            }
            
            populateData();
            showStatus('数据加载完成！', 'success');
        }
        
        function populateData() {
            if (!rankingData) return;
            
            // 更新日期
            document.getElementById('rank-date').textContent = rankingData.date;
            
            // 生成Top 3
            const top3Container = document.getElementById('top3-container');
            top3Container.innerHTML = `
                <div class="top-card" style="flex: 3; margin-top: 54px;">
                    <div class="avatar">${rankingData.rank[1].username.charAt(0)}</div>
                    <div class="name">${rankingData.rank[1].username}</div>
                    <div class="helped">Helped ${rankingData.rank[1].serviceUsers}+ Peoples</div>
                    <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn">View</a>
                </div>
                
                <div class="top-card" style="flex: 4;">
                    <div class="avatar first">🏆</div>
                    <div class="name">${rankingData.rank[0].username}</div>
                    <div class="helped">Helped ${rankingData.rank[0].serviceUsers}+ Peoples</div>
                    <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn">View</a>
                </div>
                
                <div class="top-card" style="flex: 3; margin-top: 54px;">
                    <div class="avatar">${rankingData.rank[2].username.charAt(0)}</div>
                    <div class="name">${rankingData.rank[2].username}</div>
                    <div class="helped">Helped ${rankingData.rank[2].serviceUsers}+ Peoples</div>
                    <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn">View</a>
                </div>
            `;
            
            // 生成排名列表
            const rankList = document.getElementById('rank-list');
            rankList.innerHTML = rankingData.rank.slice(3).map((user, index) => `
                <li class="rank-item">
                    <div class="rank-number">${index + 4}</div>
                    <div class="rank-avatar">${user.username.charAt(0)}</div>
                    <div class="rank-info">
                        <div class="rank-name">${user.username}</div>
                        <div class="rank-helped">Helped ${user.serviceUsers}+ Peoples</div>
                    </div>
                    <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn">View</a>
                </li>
            `).join('');
        }
