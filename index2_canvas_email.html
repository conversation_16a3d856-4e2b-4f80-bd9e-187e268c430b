<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Index2 Canvas Email Generator</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script src="https://www.cyanmoonapp.com/activity/rank/js/jquery-3.2.1.min.js"></script>
    <style>
        .generator-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .controls {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .controls h3 {
            margin-top: 0;
            color: #333;
        }
        
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #005a87;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .preview-section {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .original-preview {
            flex: 1;
            border: 1px solid #ddd;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        
        .result-preview {
            flex: 1;
            border: 1px solid #ddd;
            padding: 20px;
            background: white;
            border-radius: 8px;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .clickable-overlay {
            position: relative;
            display: inline-block;
        }
        
        .click-area {
            position: absolute;
            background: rgba(255, 0, 0, 0.1);
            border: 1px dashed red;
            cursor: pointer;
            transition: all 0.3s;
            z-index: 10;
        }
        
        .click-area:hover {
            background: rgba(255, 0, 0, 0.3);
            border: 2px solid red;
        }
        
        .click-area::after {
            content: attr(title);
            position: absolute;
            top: -25px;
            left: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .click-area:hover::after {
            opacity: 1;
        }
        
        .code-section {
            margin-top: 20px;
        }
        
        .code-section details {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
        }
        
        .code-section pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        
        /* 原始页面样式 - 从index2.html复制 */
        @font-face {
            font-family: 'Source Sans Pro';
            src: url('./font//SourceSans3-ExtraBoldItalic.ttf');
        }
        
        .container {
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/bg.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            width: 500px !important;
            height: 1700px;
            margin: 0 auto;
            background-color: #12021c;
            position: relative;
        }
        
        .main {
            position: absolute;
            top: 39%;
            width: 100%;
        }
        
        .top3 {
            display: flex;
            height: 330px;
            gap: 3%;
            width: 500px;
            margin: auto;
            display: -webkit-flex;
        }
        
        #rank-date {
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            background: linear-gradient(180deg, #FFFFFF 18%, #FFFFFF 57.32%, #FBDFAC 76%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-family: 'Source Sans Pro';
            font-size: 2em;
            font-style: normal;
            font-weight: 600;
            letter-spacing: 1px;
            text-shadow: 0px 0px 30px 0px #FFD724,1px 1px 0px 0px #372813;
        }
        
        .helped {
            font-family: Source Sans Pro;
            font-weight: 400;
            font-size: 13px;
            line-height: 100%;
            letter-spacing: 0%;
            text-align: center;
            vertical-align: middle;
            color: #a38e8d;
            margin: 20px 0 0 0;
        }
        
        .go-btn1 {
            width: 150px;
            height: 42px;
            margin: 7px 0 0 15px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/btn-1.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            border: none;
            border-radius: 22px;
            float: left;
        }
        
        .go-btn2 {
            width: 120px;
            height: 32px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/btn-2.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            border: none;
            border-radius: 22px;
            float: left;
            margin: 15px 0 0 8px;
        }
        
        .avatar1 {
            width: 104px;
            height: 104px;
            top: 16px;
            left: 28px;
            border-radius: 90px;
            border-width: 1px;
            position: absolute;
            object-fit: cover;
        }
        
        .avatar2 {
            width: 148px;
            height: 148px;
            top: 20px;
            left: 30px;
            border-radius: 90px;
            border-width: 1px;
            position: absolute;
            object-fit: cover;
        }
        
        .avatar3 {
            width: 104px;
            height: 104px;
            top: 20px;
            left: 30px;
            border-radius: 90px;
            border-width: 1px;
            position: absolute;
            object-fit: cover;
        }
        
        .name1 {
            position: absolute;
            top: 162px;
            left: 20px;
            max-width: 116px;
            color: #0f1142;
            font-weight: 700;
            font-size: 13.68px;
            line-height: 100%;
            text-align: center;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .name2 {
            position: absolute;
            top: 210px;
            left: 30px;
            max-width: 150px;
            color: #0f1142;
            font-weight: 700;
            font-size: 15.68px;
            line-height: 100%;
            text-align: center;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .name3 {
            position: absolute;
            top: 162px;
            left: 30px;
            max-width: 116px;
            color: #0f1142;
            font-weight: 700;
            font-size: 13.68px;
            line-height: 100%;
            text-align: center;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .rank {
            list-style: none;
            padding: 20px 0 20px 0;
            margin: 0 10px;
        }
        
        .rank-item {
            display: flex;
            height: 90px;
            padding: 6px 0;
        }
        
        .rank-idx {
            flex: 12%;
            text-align: center;
            font-family: My Soul;
            font-weight: 400;
            font-size: 30px;
            line-height: 100%;
            letter-spacing: 0%;
            text-align: center;
            vertical-align: middle;
            color: #f8e4c5;
            height: 90px;
            line-height: 90px;
        }
        
        .rank-avatar-c {
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top4-bg.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 90px;
            height: 90px;
        }
        
        .rank-avatar {
            width: 80px;
            height: 80px;
            border-radius: 90px;
            border-width: 1px;
            object-fit: cover;
            margin: 5px 0 0 8px;
        }
        
        .rank-name {
            color: #ffffff;
            font-family: Philosopher;
            font-weight: 700;
            font-size: 18px;
            line-height: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 12px;
        }
        
        .rank-btn {
            width: 120px;
            height: 32px;
            background-image: url(https://www.cyanmoonapp.com/activity/rank/images/btn-2.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            border: none;
            border-radius: 22px;
            margin: 30px 0 0 8px;
        }
        
        .unsubscribe {
            text-align: center;
            position: absolute;
            bottom: 0;
            left: 42%;
            padding-bottom: 20px;
        }
        
        .unsubscribe-a {
            color: #c7c3c3;
            text-decoration: none;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="generator-container">
        <h1>Index2 Canvas Email Generator</h1>
        <p>基于您的 index2.html 生成可点击的Canvas邮件版本</p>
        
        <div class="controls">
            <h3>操作控制</h3>
            <button onclick="loadOriginalData()" id="loadBtn">1. 加载动态数据</button>
            <button onclick="generateCanvasEmail()" id="canvasBtn" disabled>2. 生成Canvas邮件</button>
            <button onclick="generateImageMapEmail()" id="mapBtn" disabled>3. 生成ImageMap邮件</button>
            <button onclick="generateLayeredEmail()" id="layerBtn" disabled>4. 生成分层邮件</button>
            <button onclick="copyEmailHTML()" id="copyBtn" disabled>5. 复制邮件HTML</button>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>
        
        <div class="preview-section">
            <div class="original-preview">
                <h3>原始内容预览</h3>
                <!-- 从index2.html复制的内容 -->
                <div class="container" id="original-container" style="width: 500px; transform: scale(0.8); transform-origin: top left;">
                    <div class="rank-date-box">
                        <span class="rank-date" id="rank-date">February 09, 2025</span>
                    </div>
                    <div class="main">
                        <div class="top3">
                            <div style="flex: 3; margin-top: 54px" id="top2">
                                <div style="height: 186px; position: relative; background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top2-bg.png); background-size: 100% 100%">
                                    <img src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500112381/media/20250113/7a7f3fd9e9e715131c847651f5f9d3f7.jpg_540x0.jpg" class="avatar1" />
                                    <div class="name1">Psychic Lumina</div>
                                </div>
                                <div class="helped">Helped 200+ Peoples</div>
                                <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>
                            </div>
                            <div style="flex: 4" id="top1">
                                <div style="height: 240px; position: relative; background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top1-bg.png); background-size: 100% 100%">
                                    <img src="http://cdn.allstarmet.com/thumbnail/starmet/pro/50011234620250220/571933f28fd8f6d63a440d5444951f8b.jpg_540x0.jpg" class="avatar2" />
                                    <div class="name2">Psychic Bianca</div>
                                </div>
                                <div class="helped">Helped 144+ Peoples</div>
                                <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn1"></a>
                            </div>
                            <div style="flex: 3; margin-top: 54px" id="top3">
                                <div style="height: 186px; position: relative; background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top3-bg.png); background-size: 100% 100%">
                                    <img src="http://cdn.allstarmet.com/starmet/pro/50011165620240427/49462087e0819829a7ba90d78be3c63c.jpg?x-oss-process=image/resize,w_540,m_lfit/format,jpg" class="avatar3" />
                                    <div class="name3">Psychic Mandy</div>
                                </div>
                                <div class="helped">Helped 74+ Peoples</div>
                                <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>
                            </div>
                        </div>
                        <ul class="rank" id="rank-list">
                            <!-- 动态内容将在这里加载 -->
                        </ul>
                    </div>
                    <div class="unsubscribe">
                        <a href="unsubscribe_link" class="unsubscribe-a">unsubscribe</a>
                    </div>
                </div>
            </div>
            
            <div class="result-preview">
                <h3>生成的邮件预览</h3>
                <div id="email-result">
                    <p style="color: #666; text-align: center; padding: 40px;">
                        请先点击"加载动态数据"，然后选择生成方式
                    </p>
                </div>
            </div>
        </div>
        
        <div class="code-section" id="code-section" style="display: none;">
            <details>
                <summary><strong>查看生成的邮件HTML代码</strong></summary>
                <pre id="html-code"></pre>
            </details>
        </div>
    </div>

    <script>
        let currentEmailHTML = '';
        let isDataLoaded = false;

        // 状态管理
        function showStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }

        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        function updateButtons() {
            document.getElementById('canvasBtn').disabled = !isDataLoaded;
            document.getElementById('mapBtn').disabled = !isDataLoaded;
            document.getElementById('layerBtn').disabled = !isDataLoaded;
            document.getElementById('copyBtn').disabled = !currentEmailHTML;
        }

        // 加载动态数据 - 模拟原始的Ajax调用
        async function loadOriginalData() {
            showStatus('正在加载动态数据...', 'loading');
            document.getElementById('loadBtn').disabled = true;

            try {
                // 模拟API调用
                const response = await fetch('https://api.cyanmoonapp.com/web/augur/weekly/rank?appId=10');

                if (!response.ok) {
                    throw new Error('API调用失败，使用模拟数据');
                }

                const res = await response.json();
                populateRankingData(res.data);

            } catch (error) {
                console.log('使用模拟数据:', error.message);
                // 使用模拟数据
                const mockData = {
                    date: "February 09, 2025",
                    rank: [
                        { username: "Psychic Bianca", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/50011234620250220/571933f28fd8f6d63a440d5444951f8b.jpg_540x0.jpg", serviceUsers: 144 },
                        { username: "Psychic Lumina", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500112381/media/20250113/7a7f3fd9e9e715131c847651f5f9d3f7.jpg_540x0.jpg", serviceUsers: 200 },
                        { username: "Psychic Mandy", avatar: "http://cdn.allstarmet.com/starmet/pro/50011165620240427/49462087e0819829a7ba90d78be3c63c.jpg", serviceUsers: 74 },
                        { username: "Spiritual Lily", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 65 },
                        { username: "Mystic Rose", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 58 },
                        { username: "Oracle Sam", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 52 },
                        { username: "Psychic Luna", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 48 },
                        { username: "Divine Sarah", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 45 },
                        { username: "Cosmic Maya", avatar: "http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg", serviceUsers: 42 }
                    ]
                };
                populateRankingData(mockData);
            }

            isDataLoaded = true;
            updateButtons();
            document.getElementById('loadBtn').disabled = false;
            showStatus('数据加载完成！', 'success');
            setTimeout(hideStatus, 2000);
        }

        // 填充排名数据
        function populateRankingData(data) {
            // 更新日期
            document.getElementById('rank-date').textContent = data.date;

            // 生成排名列表 (4-9名)
            let content = '';
            data.rank.forEach(function(item, index) {
                if (index >= 3) { // 从第4名开始
                    let idx = index + 1;
                    content += `
                        <li class="rank-item">
                            <div class="rank-idx">${idx}</div>
                            <div style="flex: 22%">
                                <div class="rank-avatar-c">
                                    <img class="rank-avatar" src="${item.avatar}" />
                                </div>
                            </div>
                            <div class="rank-name" style="flex:36%;margin-left: 12px;">
                                <p class="rank-name">${item.username}</p>
                                <p class="helped" style="text-align: left!important;">Helped ${item.serviceUsers}+ Peoples</p>
                            </div>
                            <div class="rank-btn" style="flex:30%;display: contents;">
                                <a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="rank-btn"></a>
                            </div>
                        </li>
                    `;
                }
            });

            document.getElementById('rank-list').innerHTML = content;
        }

        // 获取可点击区域
        function getClickableAreas(container) {
            const areas = [];
            const containerRect = container.getBoundingClientRect();

            // 获取所有可点击元素
            const clickableElements = container.querySelectorAll('a, button, .go-btn1, .go-btn2, .rank-btn');

            clickableElements.forEach((element, index) => {
                const rect = element.getBoundingClientRect();

                // 确保元素在容器内且有有效尺寸
                if (rect.width > 0 && rect.height > 0) {
                    areas.push({
                        id: `area-${index}`,
                        coords: {
                            x: Math.round(rect.left - containerRect.left),
                            y: Math.round(rect.top - containerRect.top),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        },
                        href: element.href || element.closest('a')?.href || 'https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en',
                        text: element.textContent?.trim() || element.alt || `Button ${index + 1}`,
                        type: element.className || 'button'
                    });
                }
            });

            return areas;
        }

        // 等待图片加载完成
        function waitForImages(container) {
            const images = container.querySelectorAll('img');
            const promises = Array.from(images).map(img => {
                if (img.complete) return Promise.resolve();

                return new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = resolve; // 即使图片加载失败也继续
                    setTimeout(resolve, 5000); // 5秒超时
                });
            });

            return Promise.all(promises);
        }

        // 生成Canvas邮件
        async function generateCanvasEmail() {
            showStatus('正在生成Canvas图片...', 'loading');

            try {
                const container = document.getElementById('original-container');

                // 等待图片加载
                await waitForImages(container);

                // 生成canvas
                const canvas = await html2canvas(container, {
                    useCORS: true,
                    allowTaint: true,
                    scale: 1,
                    backgroundColor: '#12021c',
                    logging: false,
                    onclone: function(clonedDoc) {
                        // 确保克隆的文档中字体正确加载
                        const clonedContainer = clonedDoc.querySelector('#original-container');
                        if (clonedContainer) {
                            clonedContainer.style.transform = 'none';
                        }
                    }
                });

                const imageDataUrl = canvas.toDataURL('image/png', 0.9);
                const areas = getClickableAreas(container);

                // 生成Image Map HTML
                const mapAreas = areas.map(area =>
                    `<area shape="rect" coords="${area.coords.x},${area.coords.y},${area.coords.x + area.coords.width},${area.coords.y + area.coords.height}" href="${area.href}" alt="${area.text}" title="${area.text}">`
                ).join('\n            ');

                currentEmailHTML = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Star Advisors Weekly Ranking</title>
    <style type="text/css">
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #12021c;
            font-family: Arial, Helvetica, sans-serif;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #12021c;
        }
        @media screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            .ranking-image {
                width: 100% !important;
                height: auto !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td align="center" style="padding: 20px;">
                    <img src="${imageDataUrl}"
                         usemap="#ranking-map"
                         alt="Star Advisors Weekly Ranking"
                         class="ranking-image"
                         style="max-width: 500px; width: 100%; height: auto; display: block;">
                    <map name="ranking-map">
                        ${mapAreas}
                    </map>
                </td>
            </tr>
            <tr>
                <td align="center" style="padding: 10px; color: #c7c3c3; font-size: 12px;">
                    <p style="margin: 0;">
                        <a href="https://app.com/ranking" style="color: #c7c3c3;">View Interactive Version</a> |
                        <a href="unsubscribe_link" style="color: #c7c3c3;">Unsubscribe</a>
                    </p>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;

                displayEmailResult(currentEmailHTML, 'Canvas + Image Map 邮件版本');
                showStatus('Canvas邮件生成成功！', 'success');

            } catch (error) {
                console.error('生成Canvas邮件失败:', error);
                showStatus('生成失败: ' + error.message, 'error');
            }

            updateButtons();
            setTimeout(hideStatus, 3000);
        }

        // 生成分层邮件
        async function generateLayeredEmail() {
            showStatus('正在生成分层邮件...', 'loading');

            try {
                const container = document.getElementById('original-container');
                await waitForImages(container);

                const canvas = await html2canvas(container, {
                    useCORS: true,
                    allowTaint: true,
                    scale: 1,
                    backgroundColor: '#12021c',
                    logging: false,
                    onclone: function(clonedDoc) {
                        const clonedContainer = clonedDoc.querySelector('#original-container');
                        if (clonedContainer) {
                            clonedContainer.style.transform = 'none';
                        }
                    }
                });

                const imageDataUrl = canvas.toDataURL('image/png', 0.9);
                const areas = getClickableAreas(container);

                // 生成可点击覆盖层
                const clickableOverlays = areas.map((area, index) => `
                <a href="${area.href}"
                   style="
                       position: absolute;
                       left: ${area.coords.x}px;
                       top: ${area.coords.y}px;
                       width: ${area.coords.width}px;
                       height: ${area.coords.height}px;
                       display: block;
                       z-index: 10;
                       background: transparent;
                   "
                   title="${area.text}"></a>
            `).join('');

                currentEmailHTML = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Star Advisors Weekly Ranking</title>
    <style type="text/css">
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #12021c;
            font-family: Arial, Helvetica, sans-serif;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #12021c;
        }
        .clickable-overlay {
            position: relative;
            display: inline-block;
        }
        @media screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            .ranking-image {
                width: 100% !important;
                height: auto !important;
            }
            .clickable-overlay a {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td align="center" style="padding: 20px;">
                    <div class="clickable-overlay">
                        <img src="${imageDataUrl}"
                             alt="Star Advisors Weekly Ranking"
                             class="ranking-image"
                             style="max-width: 500px; width: 100%; height: auto; display: block;">
                        ${clickableOverlays}
                    </div>
                </td>
            </tr>
            <tr>
                <td align="center" style="padding: 10px; color: #c7c3c3; font-size: 12px;">
                    <p style="margin: 0;">
                        <a href="https://app.com/ranking" style="color: #c7c3c3;">View Interactive Version</a> |
                        <a href="unsubscribe_link" style="color: #c7c3c3;">Unsubscribe</a>
                    </p>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>`;

                displayEmailResult(currentEmailHTML, '分层覆盖邮件版本');
                showStatus('分层邮件生成成功！', 'success');

            } catch (error) {
                console.error('生成分层邮件失败:', error);
                showStatus('生成失败: ' + error.message, 'error');
            }

            updateButtons();
            setTimeout(hideStatus, 3000);
        }

        // 显示邮件结果
        function displayEmailResult(emailHTML, title) {
            const resultDiv = document.getElementById('email-result');

            // 创建iframe来显示邮件内容
            const iframe = document.createElement('iframe');
            iframe.style.width = '100%';
            iframe.style.height = '600px';
            iframe.style.border = '1px solid #ddd';
            iframe.style.borderRadius = '4px';

            resultDiv.innerHTML = `
                <h4>${title}</h4>
                <p style="color: #666; font-size: 14px;">预览效果（实际邮件客户端可能略有差异）</p>
            `;
            resultDiv.appendChild(iframe);

            // 写入邮件内容
            iframe.contentDocument.open();
            iframe.contentDocument.write(emailHTML);
            iframe.contentDocument.close();

            // 显示代码区域
            document.getElementById('code-section').style.display = 'block';
            document.getElementById('html-code').textContent = emailHTML;
        }

        // 生成ImageMap邮件（简化版本）
        async function generateImageMapEmail() {
            // 这个函数与generateCanvasEmail类似，但专注于Image Map
            await generateCanvasEmail();
        }

        // 复制邮件HTML
        function copyEmailHTML() {
            if (!currentEmailHTML) {
                showStatus('没有可复制的内容', 'error');
                return;
            }

            navigator.clipboard.writeText(currentEmailHTML).then(() => {
                showStatus('HTML代码已复制到剪贴板！', 'success');
                setTimeout(hideStatus, 2000);
            }).catch(err => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = currentEmailHTML;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                showStatus('HTML代码已复制到剪贴板！', 'success');
                setTimeout(hideStatus, 2000);
            });
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            updateButtons();

            // 自动加载数据（可选）
            // loadOriginalData();
        });
    </script>
</body>
</html>
