<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>可点击邮件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .image-map-demo {
            position: relative;
            display: inline-block;
            margin: 20px 0;
        }
        
        .demo-image {
            width: 500px;
            height: 300px;
            background: linear-gradient(135deg, #12021c, #2a1a3a);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .demo-content {
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 1px 1px 0px #372813, 0px 0px 30px #FFD724;
        }
        
        .demo-buttons {
            display: flex;
            justify-content: space-around;
            margin-top: 40px;
        }
        
        .demo-btn {
            background: #FFD724;
            color: #000;
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }
        
        .demo-btn:hover {
            background: #FFC107;
        }
        
        .click-overlay {
            position: absolute;
            background: rgba(255, 0, 0, 0.3);
            border: 2px dashed red;
            pointer-events: none;
            z-index: 10;
        }
        
        .click-overlay.active {
            background: rgba(0, 255, 0, 0.3);
            border: 2px solid green;
        }
        
        .overlay-label {
            position: absolute;
            top: -25px;
            left: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            white-space: nowrap;
        }
        
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .code-example pre {
            margin: 0;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .info-box {
            background: #e7f3ff;
            border-left: 4px solid #007cba;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 可点击邮件解决方案演示</h1>
        
        <div class="demo-section">
            <h2>方案1: Image Map (HTML标准，兼容性最好)</h2>
            <p>使用HTML的 <code>&lt;map&gt;</code> 标签在图片上定义可点击区域</p>
            
            <div class="image-map-demo">
                <div class="demo-image" id="demo-image">
                    <div class="demo-content">
                        <div class="demo-title">Star Advisors Top 3</div>
                        <div class="demo-buttons">
                            <a href="#rank2" class="demo-btn" id="btn1">Rank 2</a>
                            <a href="#rank1" class="demo-btn" id="btn2">Rank 1</a>
                            <a href="#rank3" class="demo-btn" id="btn3">Rank 3</a>
                        </div>
                    </div>
                </div>
                
                <!-- 可视化可点击区域 -->
                <div class="click-overlay" style="left: 50px; top: 200px; width: 80px; height: 40px;">
                    <div class="overlay-label">Rank 2</div>
                </div>
                <div class="click-overlay" style="left: 210px; top: 200px; width: 80px; height: 40px;">
                    <div class="overlay-label">Rank 1</div>
                </div>
                <div class="click-overlay" style="left: 370px; top: 200px; width: 80px; height: 40px;">
                    <div class="overlay-label">Rank 3</div>
                </div>
            </div>
            
            <div class="success-message">
                ✅ 这种方案在所有邮件客户端都能正常工作！
            </div>
            
            <div class="code-example">
                <strong>生成的邮件HTML代码示例：</strong>
                <pre><code>&lt;img src="data:image/png;base64,iVBORw0KGgo..." 
     usemap="#ranking-map" 
     alt="Weekly Ranking"
     style="max-width: 500px; width: 100%; height: auto;"&gt;

&lt;map name="ranking-map"&gt;
    &lt;area shape="rect" coords="50,200,130,240" 
          href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" 
          alt="View Rank 2"&gt;
    &lt;area shape="rect" coords="210,200,290,240" 
          href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" 
          alt="View Rank 1"&gt;
    &lt;area shape="rect" coords="370,200,450,240" 
          href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" 
          alt="View Rank 3"&gt;
&lt;/map&gt;</code></pre>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>方案2: 分层覆盖 (现代邮件客户端)</h2>
            <p>在图片上方覆盖透明的可点击链接</p>
            
            <div class="info-box">
                <strong>优势：</strong> 更灵活的样式控制<br>
                <strong>劣势：</strong> 部分邮件客户端可能不支持CSS定位
            </div>
            
            <div class="code-example">
                <strong>分层覆盖代码示例：</strong>
                <pre><code>&lt;div style="position: relative; display: inline-block;"&gt;
    &lt;img src="data:image/png;base64,..." style="display: block;"&gt;
    
    &lt;a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" 
       style="position: absolute; left: 50px; top: 200px; 
              width: 80px; height: 40px; display: block; 
              background: transparent;"&gt;&lt;/a&gt;
              
    &lt;a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" 
       style="position: absolute; left: 210px; top: 200px; 
              width: 80px; height: 40px; display: block; 
              background: transparent;"&gt;&lt;/a&gt;
&lt;/div&gt;</code></pre>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>方案3: 纯HTML布局 (最兼容)</h2>
            <p>不使用Canvas，直接用HTML和CSS重建布局</p>
            
            <div class="info-box">
                <strong>优势：</strong> 完美兼容所有邮件客户端，无CORS问题<br>
                <strong>劣势：</strong> 需要重新实现复杂的视觉效果
            </div>
            
            <div class="code-example">
                <strong>纯HTML布局示例：</strong>
                <pre><code>&lt;table width="500" cellpadding="0" cellspacing="0" 
       style="background: linear-gradient(135deg, #12021c, #2a1a3a);"&gt;
    &lt;tr&gt;
        &lt;td style="text-align: center; padding: 20px; color: white;"&gt;
            &lt;h2&gt;Star Advisors Top 3&lt;/h2&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
    &lt;tr&gt;
        &lt;td&gt;
            &lt;table width="100%"&gt;
                &lt;tr&gt;
                    &lt;td width="33%" style="text-align: center;"&gt;
                        &lt;a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" 
                           style="background: #FFD724; color: #000; padding: 10px 20px; 
                                  text-decoration: none; border-radius: 20px;"&gt;
                            Rank 2
                        &lt;/a&gt;
                    &lt;/td&gt;
                    &lt;td width="34%" style="text-align: center;"&gt;
                        &lt;a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" 
                           style="background: #FFD724; color: #000; padding: 10px 20px; 
                                  text-decoration: none; border-radius: 20px;"&gt;
                            Rank 1
                        &lt;/a&gt;
                    &lt;/td&gt;
                    &lt;td width="33%" style="text-align: center;"&gt;
                        &lt;a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" 
                           style="background: #FFD724; color: #000; padding: 10px 20px; 
                                  text-decoration: none; border-radius: 20px;"&gt;
                            Rank 3
                        &lt;/a&gt;
                    &lt;/td&gt;
                &lt;/tr&gt;
            &lt;/table&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
&lt;/table&gt;</code></pre>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎯 推荐方案</h2>
            <div class="success-message">
                <strong>推荐使用方案1 (Image Map)</strong><br>
                ✅ 兼容性最好，支持所有邮件客户端<br>
                ✅ 保持原有的精美视觉效果<br>
                ✅ 实现简单，维护方便<br>
                ✅ 符合HTML标准
            </div>
            
            <p><strong>实施步骤：</strong></p>
            <ol>
                <li>将所有跨域图片转换为base64编码</li>
                <li>使用html2canvas生成完整的页面截图</li>
                <li>根据原始布局定义可点击区域坐标</li>
                <li>生成包含Image Map的邮件HTML</li>
            </ol>
        </div>
        
        <div class="demo-section">
            <h2>📱 移动端适配</h2>
            <div class="info-box">
                为了确保在移动设备上也能正常点击，建议：
                <ul>
                    <li>使用响应式图片 (<code>max-width: 100%</code>)</li>
                    <li>可点击区域使用百分比坐标</li>
                    <li>提供降级方案（纯文本链接）</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // 演示点击效果
        document.querySelectorAll('.demo-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                alert(`点击了: ${this.textContent}`);
            });
        });
        
        // 高亮显示可点击区域
        document.querySelectorAll('.click-overlay').forEach(overlay => {
            overlay.addEventListener('mouseenter', function() {
                this.classList.add('active');
            });
            
            overlay.addEventListener('mouseleave', function() {
                this.classList.remove('active');
            });
        });
    </script>
</body>
</html>
