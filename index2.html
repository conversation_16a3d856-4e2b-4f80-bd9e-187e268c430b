<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Star Advisors Top9</title>
		<style type="text/css">

			html,
			body {
				padding: 0;
				margin: 0;
			}
            @font-face {
                font-family: 'Source Sans Pro';
                src: url('./font//SourceSans3-ExtraBoldItalic.ttf');
            }
			.container {
				background-image: url(https://www.cyanmoonapp.com/activity/rank/images/bg.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
				width: 500px !important;
				height: 1700px;
				margin: 0 auto;
				background-color: #12021c;
				position: relative;
			}
			.main {
				position: absolute;
				top: 39%;
				width: 100%;
			}
			.top3 {
				display: flex;
				height: 330px;
				gap: 3%;
				width: 500px;
				margin: auto;
				display: -webkit-flex; /* Safari */
			}
            .rank-date-box{
                padding-top: 31em;
                text-align: center;
            }
			#rank-date {
				
				color: #fff;
                background: linear-gradient(180deg, #FFFFFF 18%, #FFFFFF 57.32%, #FBDFAC 76%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
				font-family: 'Source Sans Pro';
				font-size: 2em;
				font-style: normal;
				font-weight: 600;
				letter-spacing: 1px;
                text-shadow: 0px 0px 30px 0px #FFD724,1px 1px 0px 0px #372813;
			}

			.helped {
				font-family: Source Sans Pro;
				font-weight: 400;
				font-size: 13px;
				line-height: 100%;
				letter-spacing: 0%;
				text-align: center;
				vertical-align: middle;
				color: #a38e8d;
				margin: 20px 0 0 0;
			}
			.go-btn1 {
				width: 150px;
				height: 42px;
				margin: 7px 0 0 15px;
				background-image: url(https://www.cyanmoonapp.com/activity/rank/images/btn-1.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				border: none;
				border-radius: 22px;
				float: left;
			}
			.go-btn2 {
				width: 120px;
				height: 32px;
				background-image: url(https://www.cyanmoonapp.com/activity/rank/images/btn-2.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				border: none;
				border-radius: 22px;
				float: left;
				margin: 15px 0 0 8px;
			}
			.avatar1 {
				width: 104px;
				height: 104px;
				top: 16px;
				left: 28px;
				border-radius: 90px;
				border-width: 1px;
				position: absolute;
				object-fit: cover;
			}
			.avatar2 {
				width: 148px;
				height: 148px;
				top: 20px;
				left: 30px;
				border-radius: 90px;
				border-width: 1px;
				position: absolute;
				object-fit: cover;
			}
			.avatar3 {
				width: 104px;
				height: 104px;
				top: 20px;
				left: 30px;
				border-radius: 90px;
				border-width: 1px;
				position: absolute;
				object-fit: cover;
			}
			.name1 {
				position: absolute;
				top: 162px;
				left: 20px;
				max-width: 116px;
				color: #0f1142;
				font-weight: 700;
				font-size: 13.68px;
				line-height: 100%;
				text-align: center;
				vertical-align: middle;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.name2 {
				position: absolute;
				top: 210px;
				left: 30px;
				max-width: 150px;
				color: #0f1142;
				font-weight: 700;
				font-size: 15.68px;
				line-height: 100%;
				text-align: center;
				vertical-align: middle;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.name3 {
				position: absolute;
				top: 162px;
				left: 30px;
				max-width: 116px;
				max-width: 116px;
				color: #0f1142;
				font-weight: 700;
				font-size: 13.68px;
				line-height: 100%;
				text-align: center;
				vertical-align: middle;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.rank {
				list-style: none;
				padding: 20px 0 20px 0;
				margin: 0 10px;
			}
			.rank-item {
				display: flex;
				height: 90px;
				padding: 6px 0;
			}
			.rank-idx {
				flex: 12%;
				text-align: center;
				font-family: My Soul;
				font-weight: 400;
				font-size: 30px;
				line-height: 100%;
				letter-spacing: 0%;
				text-align: center;
				vertical-align: middle;
				color: #f8e4c5;
				height: 90px;
				line-height: 90px;
			}
			.rank-avatar-c {
				background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top4-bg.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				width: 90px;
				height: 90px;
			}
			.rank-avatar {
				width: 80px;
				height: 80px;
				border-radius: 90px;
				border-width: 1px;
				object-fit: cover;
				margin: 5px 0 0 8px;
			}
			.rank-name {
				color: #ffffff;
				font-family: Philosopher;
				font-weight: 700;
				font-size: 18px;
				line-height: 100%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin-top: 12px;
			}
			.rank-btn {
				width: 120px;
				height: 32px;
				background-image: url(https://www.cyanmoonapp.com/activity/rank/images/btn-2.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				border: none;
				border-radius: 22px;
				margin: 30px 0 0 8px;
			}
			.unsubscribe {
				text-align: center;
				position: absolute;
				bottom: 0;
				left: 42%;
				padding-bottom: 20px;
			}
			.unsubscribe-a {
				color: #c7c3c3;
				text-decoration: none;
				font-size: 13px;
			}
		</style>
	</head>
	<body>
		<div class="container" style="width: 500px">
			<div class="rank-date-box">
				<span class="rank-date" id="rank-date">
                    Fberuary 09,2025
                </span>
			</div>
			<div class="main">
				<div class="top3">
					<div style="flex: 3; margin-top: 54px" id="top2">
						<div style="height: 186px; position: relative; background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top2-bg.png); background-size: 100% 100%">
							<img src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500112381/media/20250113/7a7f3fd9e9e715131c847651f5f9d3f7.jpg_540x0.jpg" class="avatar1" />
							<div class="name1">Psychic Lumina</div>
						</div>
						<div class="helped">Helped 200+ Peoples</div>
						<a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>
					</div>
					<div style="flex: 4" id="top1">
						<div style="height: 240px; position: relative;  background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top1-bg.png); background-size: 100% 100%">
							<img src="http://cdn.allstarmet.com/thumbnail/starmet/pro/50011234620250220/571933f28fd8f6d63a440d5444951f8b.jpg_540x0.jpg" class="avatar2" />
							<div class="name2">Psychic Bianca</div>
						</div>
						<div class="helped">Helped 144+ Peoples</div>
						<a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn1"></a>
					</div>
					<div style="flex: 3; margin-top: 54px" id="top3">
						<div style="height: 186px; position: relative; background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top3-bg.png); background-size: 100% 100%">
							<img src="http://cdn.allstarmet.com/starmet/pro/50011165620240427/49462087e0819829a7ba90d78be3c63c.jpg?x-oss-process=image/resize,w_540,m_lfit/format,jpg" class="avatar3" />
							<div class="name3">Psychic Mandy</div>
						</div>
						<div class="helped">Helped 74+ Peoples</div>
						<a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>
					</div>
				</div>
				<ul class="rank" id="rank-list">

                </ul>
			</div>
			<div class="unsubscribe">
				<a href="unsubscribe_link" class="unsubscribe-a">unsubscribe</a>
			</div>
		</div>
	</body>
</html>

<script type="text/javascript" src="https://www.cyanmoonapp.com/activity/rank/js/jquery-3.2.1.min.js"></script>
<script type="text/javascript">
	const host = 'https://api.cyanmoonapp.com'

	initData()

	function initData() {
		$.ajax({
			type: 'get',
			url: host + '/web/augur/weekly/rank?appId=10',
			async: true,
			dataType: 'json',
			success: function (res) {
				let date = res.data.date
				$('#rank-date').html(date)

				let rankList = res.data.rank
				let content = ''
				rankList.forEach(function (item, index, array) {
					if (index == 0) {
						// let top1Content = '<div style="height: 240px;background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top1-bg.png);background-size: 100% 100%;"><img src="' + item.avatar + '" class="avatar2"><div class="name2">' + item.username + '</div></div><div class="helped">Helped ' + item.serviceUsers + '+ Peoples</div><a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn1"></a>'
						// $('#top1').html(top1Content)
					} else if (index == 1) {
						// let top2Content = '<div style="height: 186px;background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top2-bg.png);background-size: 100% 100%;"><img src="' + item.avatar + '" class="avatar1"><div class="name1">' + item.username + '</div></div><div class="helped">Helped ' + item.serviceUsers + '+ Peoples</div><a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>'
						// $('#top2').html(top2Content)
					} else if (index == 2) {
						// let top3Content = '<div style="height: 186px;background-image: url(https://www.cyanmoonapp.com/activity/rank/images/top3-bg.png);background-size: 100% 100%;"><img src="' + item.avatar + '" class="avatar3"><div class="name3">' + item.username + '</div></div><div class="helped">Helped ' + item.serviceUsers + '+ Peoples</div><a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="go-btn2"></a>'
						// $('#top3').html(top3Content)
					} else {
						let idx = index + 1
						content +=
							'<li class="rank-item"><div class="rank-idx">' +
							idx +
							'</div><div style="flex: 22%"><div class="rank-avatar-c"><img class="rank-avatar" src="' +
							item.avatar +
							'" /></div></div><div class="rank-name" style="flex:36%;margin-left: 12px;"><p class="rank-name">' +
							item.username +
							'</p><p class="helped" style="text-align: left!important;">Helped ' +
							item.serviceUsers +
							'+ Peoples</p></div><div class="rank-btn" style="flex:30%;display: contents;"><a href="https://play.google.com/store/apps/details?id=com.cyanmoon&hl=en" class="rank-btn"></a></div></li>'
					}
				})
				$('#rank-list').html(content)
			},
		})
	}

	$('.unsubscribe-a').click(function (e) {
		e.preventDefault() // 阻止页面跳转
		let url = this.href
		$.ajax({
			type: 'get',
			url: url,
			async: true,
			success: function (res) {
				alert('The unsubscription is successful')
			},
		})
	})
</script>
